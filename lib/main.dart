import 'dart:io';

import 'package:background_fetch/background_fetch.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/firebase_options.dart';
import 'package:quran_broadcast_app/src/app.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/background.service.dart'
    as bg;
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/initialize.dart';
import 'package:xr_helper/xr_helper.dart';

/// Top-level background messaging handler with proper Firebase initialization
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Ensure Firebase is initialized for background context
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  Log.i(
      'Background message received: ${message.notification?.title ?? 'No title'}');
  // Add any additional background message processing here
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await initialize();

    await HomeWidgetService.initialize();

    if (Platform.isAndroid) {
      BackgroundFetch.registerHeadlessTask(bg.backgroundFetchHeadlessTask);
    }
  } catch (e) {
    Log.e('Initialization Error: $e');
  }

  runApp(
    const ProviderScope(
      child: BaseApp(),
    ),
  );
}
